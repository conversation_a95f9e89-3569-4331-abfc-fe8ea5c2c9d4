import os.path as osp
import os
os.environ["OPENCV_IO_MAX_IMAGE_PIXELS"] = str(pow(2, 50))
import time
import yaml
import numpy as np
import pycuda.driver as cuda #cuda和tensorrt放在cupy之前,否则运行会出错

import tensorrt as trt
import cv2
import gc
from pathlib import Path
from loguru import logger as log
import traceback
import cupy as cp
from cupyx.scipy import ndimage
AIcrop_model = None


class GPUPredictorUtil(object):
    def __init__(self):
        self.max_concurrent_tasks=1
        self._shared_context=None
        self._shared_stream=None
        self._shared_execution_contexts= {}
        self.gpu_id = 0
        self.allocations = None
        self.engine = None
        self.context = None
        self.inputs = None
        self.outputs = None
        self.device = None
        self.ctx = None
        self.model_file_path = None
        self.SAMmodel=None

        self.ws = None
        self.root_url = 'ws://localhost:12318/ws'  # 这里输入websocket的url
        self.send_response=None
        os.environ['CUDA_VISIBLE_DEVICES'] = f"{self.gpu_id}"


    def release(self):
        self.context.__del__()
        self.engine.__del__()
        if self.allocations:
            for allocation in self.allocations:
                allocation.free()
        
        self.allocations = None
        self.inputs = None
        self.outputs = None
        self.engine = None
        self.context = None
        self.SAMmodel=None
        self.gpu_id = 0
        self.ctx = None
        cuda.init()
        self.device = cuda.Device(self.gpu_id)
        self.ctx = self.device.retain_primary_context()
        gc.collect()
        log.info("release model{}".format(self.model_file_path))
        return None


def normalize(im, mean, std):
    # Rescaling (min-max normalization)
    try:
        if isinstance(im, np.ndarray):
            im = cp.array(im)
        im = im / 255.0
        # Standardization (Z-score Normalization)
        im -= mean
        im /= std
    except Exception as e:
        log.error(f"cupy normalize error: {e}, falling back to numpy")
        if isinstance(im, cp.ndarray):
            im = im.get()
        im = im / 255.0
        # Standardization (Z-score Normalization)
        if isinstance(mean, cp.ndarray):
            mean = mean.get()
        if isinstance(std, cp.ndarray):
            std = std.get()
        im -= mean
        im /= std
    return im.astype('float32')


def permute(im, to_bgr=False):
    try:
        if isinstance(im, np.ndarray):
            im = cp.array(im)
        im = cp.swapaxes(im, 1, 2)
        im = cp.swapaxes(im, 1, 0)
    except Exception as e:
        log.error(f"cupy permute error: {e}, falling back to numpy")
        if isinstance(im, cp.ndarray):
            im = im.get()
        im = np.swapaxes(im, 1, 2)
        im = np.swapaxes(im, 1, 0)
    if to_bgr:
        im = im[[2, 1, 0], :, :]
    return im

def resize_long(im, long_size=224, interpolation=cv2.INTER_LINEAR):
    value = max(im.shape[0], im.shape[1])
    scale = float(long_size) / float(value)
    resized_width = int(round(im.shape[1] * scale))
    resized_height = int(round(im.shape[0] * scale))
    zoom_factor = [resized_height / im.shape[0], resized_width / im.shape[1]]
    if len(im.shape) == 3:  # 如果是彩色图像
        zoom_factor.append(1.0)
    order = 1 if interpolation == cv2.INTER_LINEAR else 0
    try:
        if isinstance(im, np.ndarray):
            im = cp.array(im)
        im = ndimage.zoom(im, zoom_factor, order=order)
    except Exception as e:
        log.error(f"cupy resize error: {e}, falling back to OpenCV")
        if isinstance(im, cp.ndarray):
            im = im.get()
        im = cv2.resize(im, (resized_width, resized_height), interpolation=interpolation)
    return im


def resize(im, target_size=(608,608), interp=cv2.INTER_LINEAR):
    if isinstance(target_size, list) or isinstance(target_size, tuple):
        w = target_size[0]
        h = target_size[1]
    else:
        w = target_size
        h = target_size
    zoom_factor = [h / im.shape[0], w / im.shape[1]]
    if len(im.shape) == 3:  # 如果是彩色图像
        zoom_factor.append(1.0)
    # 设置插值方法
    order = 1 if interp == cv2.INTER_LINEAR else 0
    try:
        if isinstance(im, np.ndarray):
            im = cp.array(im)
        im = ndimage.zoom(im, zoom_factor, order=order)
    except Exception as e:
        log.error(f"cupy resize error: {e}, falling back to OpenCV")
        if isinstance(im, cp.ndarray):
            im = im.get()
        im = cv2.resize(im, (w, h), interpolation=interp)
    return im


def resize_padding(im, max_side_len=2400):
    h, w, _ = im.shape

    resize_w = w
    resize_h = h

    # limit the max side
    if max(resize_h, resize_w) > max_side_len:
        ratio = float(
            max_side_len) / resize_h if resize_h > resize_w else float(
            max_side_len) / resize_w
    else:
        ratio = 1.
    resize_h = int(resize_h * ratio)
    resize_w = int(resize_w * ratio)

    resize_h = resize_h if resize_h % 32 == 0 else (resize_h // 32 - 1) * 32
    resize_w = resize_w if resize_w % 32 == 0 else (resize_w // 32 - 1) * 32
    resize_h = max(32, resize_h)
    resize_w = max(32, resize_w)
    zoom_factor = [resize_h / im.shape[0], resize_w / im.shape[1]]
    if len(im.shape) == 3:  # 如果是彩色图像
        zoom_factor.append(1.0)
    try:
        if isinstance(im, np.ndarray):
            im = cp.array(im)
        im = ndimage.zoom(im, zoom_factor, order=1)
        ratio_h = resize_h / float(h)
        ratio_w = resize_w / float(w)
        _ratio = cp.array([ratio_h, ratio_w]).reshape(-1, 2)
    except Exception as e:
        log.error(f"cupy resize error: {e}, falling back to OpenCV")
        if isinstance(im, cp.ndarray):
            im = im.get()
        im = cv2.resize(im, (int(resize_w), int(resize_h)))
        ratio_h = resize_h / float(h)
        ratio_w = resize_w / float(w)
        _ratio = np.array([ratio_h, ratio_w]).reshape(-1, 2)
    return im, _ratio


class AIcropInferengine(GPUPredictorUtil):
    def __init__(self,model_file_path = None,max_shape=1024):
        super().__init__()
        try:
            current_path = Path(__file__).resolve()
            config_yaml= os.path.join(current_path, "AI_crophole_APP", "config.yaml")
            for i in range(3):
                parents_path = Path(__file__).resolve().parents[i]
                yaml_file = os.path.join(parents_path, "AI_crophole_APP", "config.yaml")
                if os.path.exists(yaml_file):
                    config_yaml=yaml_file
                    current_path=parents_path
                    log.info(f"{current_path} {config_yaml}")
            model_config = yaml.load(open(config_yaml, encoding='utf-8'), Loader=yaml.FullLoader)
            model_file_path = os.path.join(current_path, model_config["kuang_predictor"]["engine"])
            max_shape=model_config["kuang_predictor"]["max_shape"]
            log.info(f"load {config_yaml} success\n {model_config} \n model_file_path {model_file_path} \n max_shape {max_shape}")
        except Exception as e:
            config_yaml = r"G:\openvino_pyinstaller\RMQ_softwareV2_junyihua_V2.0.50_4channel_chaifen_heartbeat_aidraw\P1_APP\AI_crophole_APP\config.yaml"
            if os.path.exists(config_yaml):
                model_config = yaml.load(open(config_yaml, encoding='utf-8'), Loader=yaml.FullLoader)
                model_file_path = os.path.join(
                    r"G:\openvino_pyinstaller\RMQ_softwareV2_junyihua_V2.0.50_4channel_chaifen_heartbeat_aidraw\P1_APP",
                    model_config["kuang_predictor"]["engine"])
                max_shape = model_config["kuang_predictor"]["max_shape"]
            log.error(f"load config file fail {e}")
        if not osp.exists(model_file_path):
            log.error("model engine file is not exists in {}".format(model_file_path))
        log.info(f"crop hole model_file_path {model_file_path} input_max_shape {max_shape}")
        self.model_file_path = model_file_path
        try:
            self._init_cuda_context()
            self._setup_logging()
            self.engine = self._load_engine(model_file_path)
            log.info(f"context shape {self.engine.create_execution_context().get_binding_shape(0)}")
        except Exception as e:
            log.info(f"加载TensorRT引擎失败:{str(e)}")
            from .export_trt import EngineBuilder
            if os.path.exists(model_file_path.replace(".engine", ".onnx")):
                log.info("find onnx model,try to load onnx model convert to engine")
                builder = EngineBuilder(verbose=False)
                builder.get_engine(model_file_path.replace(".engine", ".onnx"), model_file_path)
            else:
                log.error(
                    f"{model_file_path.replace('.engine', '.onnx')} file is not exists,not convert model to engine,pls check")
            self.engine = self._load_engine(model_file_path)
        self.context = self.engine.create_execution_context()
        self.model_is_dynamic = -1 in self.context.get_binding_shape(0)
        self.max_batch_size, self.max_shape = self._get_engine_profile()
        self.max_shape=min(self.max_shape,max_shape)
        log.info(f"max_batch {self.max_batch_size} max_shape {self.max_shape} model_is_dynamic {self.model_is_dynamic}")
        self.to_bgr = True
        self.mean = cp.array([0.5, 0.5, 0.5])
        self.std = cp.array([0.5, 0.5, 0.5])
        self.mempool = cp.get_default_memory_pool()

    def _load_engine(self, path):
        """加载TensorRT引擎"""
        with open(path, "rb") as f, trt.Runtime(trt.Logger(trt.Logger.ERROR)) as runtime:
            return runtime.deserialize_cuda_engine(f.read())

    def _get_engine_profile(self):
        """获取引擎优化配置"""
        input_names = [name for name in self.engine if self.engine.get_tensor_mode(name) == trt.TensorIOMode.INPUT]
        profile_idx = [i for i in range(self.engine.num_optimization_profiles)]
        shapes = self.engine.get_tensor_profile_shape(input_names[0], profile_idx[0])
        log.info(f"_get_engine_profile {shapes}")
        max_batch_size = shapes[2][0]  # 取max shape的batch维度
        max_shape = shapes[2][-1]  # 取max shape的H/W维度
        return max_batch_size, max_shape

    def _init_cuda_context(self):
        """初始化CUDA上下文"""
        cuda.init()
        self.device = cuda.Device(0)
        self.ctx = self.device.retain_primary_context()

    def _setup_logging(self):
        """设置日志信息"""
        log.info(f"TensorRT Version: {trt.__version__}")
        log.info(f"Device: {self.device.name()}")
        log.info(f"Compute Capability: {self.device.compute_capability()}")
        log.info(f"Total Memory: {self.device.total_memory() // (1024 ** 3)} GB")

    def allocate_buffers(self, input_shape=None):
        self.inputs = []
        self.outputs = []
        self.allocations = []
        self.ctx.push()
        for i in range(self.engine.num_io_tensors):
            name = self.engine.get_binding_name(i)
            dtype = self.engine.get_binding_dtype(i)
            if self.engine.binding_is_input(i):
                if -1 in tuple(self.engine.get_binding_shape(i)):  # dynamic
                    self.context.set_binding_shape(i, tuple(input_shape))
            shape = tuple(self.context.get_binding_shape(i))
            size = np.dtype(trt.nptype(dtype)).itemsize
            for s in shape:
                size *= s
            allocation = cuda.mem_alloc(size)
            binding = {
                'index': i,
                'name': name,
                'dtype': np.dtype(trt.nptype(dtype)),
                'shape': list(shape),
                'allocation': allocation,
            }
            self.allocations.append(allocation)
            if self.engine.binding_is_input(i):
                self.inputs.append(binding)
            else:
                self.outputs.append(binding)
        self.ctx.pop()
        assert len(self.inputs) > 0
        assert len(self.outputs) > 0
        assert len(self.allocations) > 0

    def preprocess(self, image):
        res = dict()
        # print("image.shape", image.shape)
        im_info = [('resize', image.shape[:2])]
        if self.model_is_dynamic:
            if max(image.shape) > self.max_shape:
                image = resize(image, self.max_shape)
        else:
            image = resize(image, self.max_shape)
        im = normalize(image, self.mean, self.std)
        im = permute(im, to_bgr=self.to_bgr)
        # print(self.dynamic_mode, im_info)
        # print("im.shape",im.shape)
        if isinstance(im, cp.ndarray):
            im = im.get()
        im = np.expand_dims(im, axis=0)
        res['image'] = im
        res['im_info'] = im_info
        return res

    def predict(self, img_file):
        # Create builder
        preprocessed_inputs = self.preprocess(img_file)
        try:
            self.allocate_buffers(preprocessed_inputs["image"].shape)
            cuda.memcpy_htod(self.inputs[0]['allocation'], np.ascontiguousarray(preprocessed_inputs['image']))
        except:
            log.error(f"engine error and reboot engine",exc_info=True)
            self.release()
            self.__init__(self.model_file_path)
            self.allocate_buffers(preprocessed_inputs["image"].shape)
            cuda.memcpy_htod(self.inputs[0]['allocation'], np.ascontiguousarray(preprocessed_inputs['image']))
        self.context.execute_v2(self.allocations)
        output = np.zeros(self.outputs[0]['shape'], dtype=self.outputs[0]['dtype'])
        cuda.memcpy_dtoh(output, self.outputs[0]['allocation'])
        try:
            label_map = cp.squeeze(cp.array(output[0])).astype('uint8')
        except:
            log.error("cupy squeeze error, falling back to numpy",exc_info=True)
            label_map = np.squeeze(np.array(output[0])).astype('uint8')
        im_info = preprocessed_inputs['im_info']
        for info in im_info[::-1]:
            if info[0] == 'resize':
                w, h = info[1][1], info[1][0]
                label_map = resize(label_map, target_size=(w, h), interp=cv2.INTER_NEAREST)
            elif info[0] == 'padding':
                w, h = info[1][1], info[1][0]
                label_map = label_map[0:h, 0:w]
        if isinstance(label_map, cp.ndarray):
            label_map = label_map.get()
            self.mempool.free_all_blocks()
        # print("label_map.shape", label_map.shape)
        return label_map



def extract_contours(labelmap):
    img_h, img_w = labelmap.shape
    center_h, center_w = int(img_h / 2), int(img_w / 2)
    try:
        c = cv2.findContours(labelmap, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE, offset=(-1, -1))[0]
        log.info('原始检测孔数:' + str(len(c)))
        # print('原始检测孔数:', len(c))
        if len(c):
            center_con = []
            for c_n in range(len(c)):
                x, y, w, h = cv2.boundingRect(c[c_n])
                x_r, y_r = x + w, y + h
                # 在原图上画出预测的矩形
                # cv2.rectangle(label_map, (x, y), (x + w, y + h), (255, 255, 255), 30)

                # 判断中心点在矩形框内
                if x <= center_w <= x_r and y <= center_h <= y_r:
                    center_con.append(c[c_n])
            # print('处理后孔数:', len(center_con))
            label_map_new = np.zeros(labelmap.shape, dtype='uint8')
            if len(center_con) == 1:
                log.info('中心点区域处理后孔数:' + str(len(center_con)))
                cv2.drawContours(label_map_new, center_con, -1, 1, -1)  # 绘图
            else:  # 取最大的区域
                d = [max(center_con, key=cv2.contourArea)]
                cv2.drawContours(label_map_new, d, -1, 1, -1)  # 绘图
                log.info('最大区域处理后孔数:' + str(len(center_con)))
            labelmap=label_map_new
    except:
        log.info("轮廓未检测到孔",exc_info=True)
    return labelmap


# view_judge  0: slide 1: kong
def crop_hole(cv_img, kong_num, lens, view_judge):
    global AIcrop_model
    start_time = time.time()
    kong_num=kong_num
    lens=lens
    log.info(f"view_judge {view_judge}")
    h_im, w_im = cv_img.shape[:2]
    mask_im = np.ones((h_im, w_im), dtype=np.uint8)
    if int(view_judge) == 0:
        log.info(f"软件传参 {view_judge},不执行扣孔操作")
        return mask_im
    else:
        if AIcrop_model is None:
            log.info("init AIcrop_model")
            try:
                AIcrop_model = AIcropInferengine()
                log.info(f"init AIcrop_model cost time {time.time() - start_time} s")
            except Exception as e:
                log.error(f"init AIcrop_model fail {e}")
                error_message = traceback.format_exc()
                log.error(f"{error_message}")
        try:
            mask_im=AIcrop_model.predict(cv_img)
            #print(cv_img.shape,mask_im.shape)
            mask_im=extract_contours(mask_im)
            ker_num = int(max(mask_im.shape) * 0.03)
            if ker_num % 2 == 0:
                ker_num = ker_num + 1
            #mask_im = cv2.erode(mask_im, (int(ker_num), int(ker_num)),iterations=30)
            mask_im = cv2.blur(mask_im, (int(ker_num), int(ker_num))) #变平滑
            log.info(f"crop hole cost time {time.time() - start_time} s")
        except Exception as e:
            log.error(f"crop hole fail,{e}")
            error_message = traceback.format_exc()
            log.error(f"{error_message}")
    return mask_im


def crop_muti_image(cv_im):
    return [[0, 0, cv_im.shape[1], cv_im.shape[0]]]
if __name__ == '__main__':
    from imutils.paths import list_images
    for img_path in list_images(r"H:\Raw Data - 副本\test_data\imageTemp\107 - 副本\images"):
        cv_img = cv2.imdecode(np.fromfile(img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
        mask_im=crop_hole(cv_img,25,7,1)
        result = cv2.bitwise_and(cv_img, cv_img, mask=mask_im)
        cv2.imencode('.jpg', result)[1].tofile(img_path)
        #print(np.sum(mask_im))
